import { NextRequest, NextResponse } from 'next/server';
import { getAICollegeRecommendations, getAIEssayAnalysis, StudentProfile } from '@/lib/ai-service';

export async function POST(request: NextRequest) {
  try {
    const formData: any = await request.json();

    // Validate form data
    if (!formData.gpa || !formData.testScore || !formData.essay) {
      return NextResponse.json(
        { message: 'GPA, test score, and essay are required' },
        { status: 400 }
      );
    }

    // Create student profile for AI analysis
    const studentProfile: StudentProfile = {
      gpa: formData.gpa,
      testScore: formData.testScore,
      testType: formData.testType,
      hasSports: formData.sports || false,
      extracurriculars: formData.extracurriculars,
      essay: formData.essay
    };

    // Get AI-powered college recommendations (now includes all features for everyone)
    const aiRecommendations = await getAICollegeRecommendations(studentProfile, true);

    // Get AI essay analysis (now included for everyone)
    const essayAnalysis = await getAIEssayAnalysis(formData.essay);

    // Convert AI recommendations to our format
    const selectedColleges = aiRecommendations.map(rec => ({
      ...rec.college,
      matchScore: rec.matchPercentage,
      aiReasoning: rec.reasoning,
      strengthsAlignment: rec.strengthsAlignment,
      improvementAreas: rec.improvementAreas
    }));

    // Create prediction ID
    const predictionId = 'pred_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // Create prediction object (no database storage)
    const prediction = {
      id: predictionId,
      created_at: new Date().toISOString(),
      form_data: formData,
      suggested_colleges: selectedColleges.slice(0, 3), // Show top 3 for main display
      essay_analysis: essayAnalysis,
      is_premium: true, // Everyone gets premium features now
      all_recommendations: selectedColleges // Store all recommendations
    };

    return NextResponse.json({
      message: 'Prediction created successfully',
      prediction,
      predictionId,
      redirectUrl: `/prediction/results/${predictionId}`
    });
  } catch (error) {
    console.error('Prediction creation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}


