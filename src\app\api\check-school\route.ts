import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize the Gemini AI client
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY || 'demo-key');

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { schoolName, studentProfile } = body;

    if (!schoolName || !studentProfile) {
      return NextResponse.json(
        { error: 'School name and student profile are required' },
        { status: 400 }
      );
    }

    // Get AI-powered school acceptance prediction
    const prediction = await getSchoolAcceptancePrediction(schoolName, studentProfile);

    return NextResponse.json({
      success: true,
      schoolName,
      prediction
    });

  } catch (error) {
    console.error('School check error:', error);
    return NextResponse.json(
      { error: 'Failed to check school acceptance' },
      { status: 500 }
    );
  }
}

async function getSchoolAcceptancePrediction(schoolName: string, profile: any) {
  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Safely extract extracurriculars
    const extracurriculars = profile.extracurriculars ?
      Object.entries(profile.extracurriculars)
        .filter(([_, value]) => value)
        .map(([key, _]) => key)
        .join(', ') : 'None specified';

    const prompt = `You are an expert college admissions counselor. Analyze this student's realistic chances at ${schoolName} using ACTUAL admission data and standards.

STUDENT PROFILE:
- GPA: ${profile.gpa}/4.0 (Unweighted)
- ${profile.testType} Score: ${profile.testScore}
- Class Rank: ${profile.classRank || 'Not provided'}
- AP/IB Courses: ${profile.apCourses || 'Not provided'}
- Intended Major: ${profile.intendedMajor || 'Undecided'}
- Sports: ${profile.sports ? 'Yes' : 'No'}
- Work Experience: ${profile.workHours || 0} hours/week
- Community Service: ${profile.communityService || 0} hours
- First Generation: ${profile.firstGeneration ? 'Yes' : 'No'}
- Extracurriculars: ${extracurriculars}
- Awards: ${profile.awards || 'None listed'}
- Career Goals: ${profile.careerGoals || 'Not specified'}
- Challenges Overcome: ${profile.challenges || 'None listed'}
- Unique Experiences: ${profile.uniqueExperiences || 'None listed'}

ANALYSIS INSTRUCTIONS:
1. Research ${schoolName}'s ACTUAL admission statistics (average GPA, test scores, acceptance rate)
2. Compare student's stats to the school's 25th-75th percentile ranges
3. Consider the school's specific selectivity level and competition
4. Factor in intended major competitiveness at this specific school
5. Be REALISTIC about chances - most top schools reject qualified students

DECISION CATEGORIES (be conservative):
- "STRONG CHANCE": Student well above averages + exceptional profile (75-90% confidence)
- "GOOD CHANCE": Student meets/exceeds averages + solid profile (60-74% confidence)
- "POSSIBLE CHANCE": Student near averages + some strengths (50-59% confidence)
- "LOW CHANCE": Student below averages but not impossible (50-55% confidence)

IMPORTANT: For highly selective schools (under 15% acceptance), even perfect students should get max 85% confidence.

RESPOND WITH VALID JSON ONLY:
{
  "decision": "GOOD CHANCE",
  "confidence": 68,
  "reasoning": "Your ${profile.gpa} GPA and ${profile.testScore} ${profile.testType} score are [compare to school's actual averages]. ${schoolName} typically admits students with [specific stats]. With their [actual acceptance rate]% acceptance rate and [thousands/hundreds] of applicants, admission is highly competitive. Your [specific strengths] help your profile, but [specific areas] need strengthening.",
  "strengths": ["Specific strength based on school's values", "Another relevant strength"],
  "improvements": ["Specific improvement for this school", "Another targeted suggestion"],
  "schoolSpecificTips": ["Research specific programs at ${schoolName}", "Connect with [specific departments/professors]", "Highlight fit with school's mission", "Apply early if it's your top choice"]
}`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();



    const cleanText = text.replace(/```json\n?|\n?```/g, '').trim();
    const analysis = JSON.parse(cleanText);

    // Ensure all required fields with safe defaults
    return {
      decision: analysis.decision || 'ACCEPT',
      confidence: Math.min(95, Math.max(50, analysis.confidence || 75)),
      reasoning: analysis.reasoning || `Based on your ${profile.gpa} GPA and ${profile.testScore} ${profile.testType} score, you have a solid chance at ${schoolName}.`,
      strengths: analysis.strengths || ['Strong academic foundation'],
      improvements: analysis.improvements || ['Continue current trajectory'],
      schoolSpecificTips: analysis.schoolSpecificTips || ['Research specific programs', 'Write compelling essays', 'Apply early if possible']
    };

  } catch (error) {
    console.error('AI school prediction error:', error);

    // Fallback prediction
    return getFallbackPrediction(schoolName, profile);
  }
}

function getFallbackPrediction(schoolName: string, profile: any) {
  // Comprehensive school database with real admission data
  const schoolData = getSchoolAdmissionData(schoolName);

  // Calculate academic competitiveness
  const gpaRatio = profile.gpa / schoolData.avgGPA;
  const testTarget = profile.testType === 'SAT' ? schoolData.avgSAT : schoolData.avgACT;
  const testRatio = profile.testScore / testTarget;

  // Academic score (0-100)
  let academicScore = ((gpaRatio + testRatio) / 2) * 100;

  // Apply selectivity penalty based on acceptance rate
  const selectivityPenalty = schoolData.acceptanceRate < 5 ? 35 :
                            schoolData.acceptanceRate < 10 ? 25 :
                            schoolData.acceptanceRate < 20 ? 15 :
                            schoolData.acceptanceRate < 40 ? 8 : 0;

  academicScore -= selectivityPenalty;

  // Holistic factors
  const extracurricularCount = Object.values(profile.extracurriculars || {}).filter(Boolean).length;
  const communityServiceHours = parseInt(profile.communityService) || 0;
  const workHours = parseInt(profile.workHours) || 0;

  const ecBonus = Math.min(12, extracurricularCount * 2);
  const serviceBonus = Math.min(8, communityServiceHours / 25);
  const workBonus = Math.min(5, workHours / 8);
  const awardsBonus = profile.awards && profile.awards !== 'None listed' ? 6 : 0;
  const firstGenBonus = profile.firstGeneration ? 4 : 0;
  const sportsBonus = profile.sports && schoolData.acceptanceRate < 20 ? 3 : 0;

  const finalScore = Math.round(academicScore + ecBonus + serviceBonus + workBonus + awardsBonus + firstGenBonus + sportsBonus);
  const confidence = Math.min(85, Math.max(50, finalScore));

  // Determine decision based on score and school selectivity
  let decision;
  if (schoolData.acceptanceRate > 70) {
    // Safety schools - be more generous
    if (finalScore >= 65) decision = 'STRONG CHANCE';
    else if (finalScore >= 50) decision = 'GOOD CHANCE';
    else decision = 'POSSIBLE CHANCE';
  } else if (schoolData.acceptanceRate > 40) {
    // Match schools
    if (finalScore >= 75) decision = 'STRONG CHANCE';
    else if (finalScore >= 60) decision = 'GOOD CHANCE';
    else if (finalScore >= 50) decision = 'POSSIBLE CHANCE';
    else decision = 'LOW CHANCE';
  } else if (schoolData.acceptanceRate > 15) {
    // Competitive schools
    if (finalScore >= 80) decision = 'GOOD CHANCE';
    else if (finalScore >= 65) decision = 'POSSIBLE CHANCE';
    else decision = 'LOW CHANCE';
  } else {
    // Highly selective schools
    if (finalScore >= 85) decision = 'GOOD CHANCE';
    else if (finalScore >= 70) decision = 'POSSIBLE CHANCE';
    else decision = 'LOW CHANCE';
  }

  // Generate realistic reasoning
  const gpaComparison = profile.gpa >= schoolData.avgGPA ? 'meets/exceeds' : 'falls below';
  const testComparison = profile.testScore >= testTarget ? 'competitive' : 'below average';

  return {
    decision,
    confidence,
    reasoning: `Your ${profile.gpa} GPA ${gpaComparison} ${schoolName}'s typical ${schoolData.avgGPA} average, and your ${profile.testScore} ${profile.testType} is ${testComparison} compared to their ${testTarget} average. With a ${schoolData.acceptanceRate}% acceptance rate, ${schoolName} is ${schoolData.selectivityLevel}. Your ${extracurricularCount} extracurricular activities and ${communityServiceHours} service hours ${finalScore >= 65 ? 'strengthen' : 'help'} your profile.`,
    strengths: [
      ...(profile.gpa >= schoolData.avgGPA ? ['GPA meets school standards'] : []),
      ...(profile.testScore >= testTarget ? ['Competitive test scores'] : []),
      ...(extracurricularCount > 3 ? ['Strong extracurricular involvement'] : []),
      ...(communityServiceHours > 100 ? ['Significant community service commitment'] : []),
      ...(profile.awards && profile.awards !== 'None listed' ? ['Academic achievements'] : []),
      ...(profile.sports ? ['Athletic participation'] : [])
    ].slice(0, 3),
    improvements: [
      ...(profile.gpa < schoolData.avgGPA ? [`Improve GPA (school average: ${schoolData.avgGPA})`] : []),
      ...(profile.testScore < testTarget ? [`Retake ${profile.testType} (school average: ${testTarget})`] : []),
      ...(extracurricularCount < 3 ? ['Develop more meaningful extracurricular activities'] : []),
      ...(communityServiceHours < 50 ? ['Increase community service involvement'] : []),
      'Write compelling essays that demonstrate genuine interest and fit'
    ].slice(0, 3),
    schoolSpecificTips: [
      `Research ${schoolName}'s specific ${profile.intendedMajor || 'academic'} programs`,
      schoolData.acceptanceRate < 15 ? 'Apply early decision if this is your top choice' : 'Consider early application',
      `Connect your experiences to ${schoolName}'s mission and values`,
      'Seek strong recommendation letters from teachers in your intended field'
    ]
  };
}

function getSchoolAdmissionData(schoolName: string) {
  const schoolLower = schoolName.toLowerCase();

  // Comprehensive database of real admission data
  const schoolDatabase: { [key: string]: any } = {
    'harvard': { avgGPA: 4.18, avgSAT: 1520, avgACT: 34, acceptanceRate: 3.4, selectivityLevel: 'extremely competitive' },
    'yale': { avgGPA: 4.14, avgSAT: 1515, avgACT: 34, acceptanceRate: 4.6, selectivityLevel: 'extremely competitive' },
    'princeton': { avgGPA: 4.16, avgSAT: 1510, avgACT: 34, acceptanceRate: 4.0, selectivityLevel: 'extremely competitive' },
    'stanford': { avgGPA: 4.18, avgSAT: 1505, avgACT: 34, acceptanceRate: 3.9, selectivityLevel: 'extremely competitive' },
    'mit': { avgGPA: 4.17, avgSAT: 1535, avgACT: 35, acceptanceRate: 4.1, selectivityLevel: 'extremely competitive' },
    'columbia': { avgGPA: 4.15, avgSAT: 1505, avgACT: 34, acceptanceRate: 3.9, selectivityLevel: 'extremely competitive' },
    'upenn': { avgGPA: 4.10, avgSAT: 1500, avgACT: 33, acceptanceRate: 5.9, selectivityLevel: 'extremely competitive' },
    'dartmouth': { avgGPA: 4.11, avgSAT: 1490, avgACT: 33, acceptanceRate: 6.2, selectivityLevel: 'extremely competitive' },
    'brown': { avgGPA: 4.08, avgSAT: 1485, avgACT: 33, acceptanceRate: 5.4, selectivityLevel: 'extremely competitive' },
    'cornell': { avgGPA: 4.05, avgSAT: 1470, avgACT: 33, acceptanceRate: 8.7, selectivityLevel: 'highly competitive' },
    'duke': { avgGPA: 4.13, avgSAT: 1520, avgACT: 34, acceptanceRate: 6.2, selectivityLevel: 'extremely competitive' },
    'northwestern': { avgGPA: 4.09, avgSAT: 1490, avgACT: 33, acceptanceRate: 7.0, selectivityLevel: 'extremely competitive' },
    'berkeley': { avgGPA: 4.25, avgSAT: 1430, avgACT: 32, acceptanceRate: 14.5, selectivityLevel: 'highly competitive' },
    'ucla': { avgGPA: 4.23, avgSAT: 1425, avgACT: 32, acceptanceRate: 12.3, selectivityLevel: 'highly competitive' },
    'michigan': { avgGPA: 4.00, avgSAT: 1420, avgACT: 32, acceptanceRate: 20.2, selectivityLevel: 'very competitive' },
    'virginia': { avgGPA: 4.05, avgSAT: 1410, avgACT: 31, acceptanceRate: 21.0, selectivityLevel: 'very competitive' },
    'georgia tech': { avgGPA: 4.10, avgSAT: 1400, avgACT: 31, acceptanceRate: 17.0, selectivityLevel: 'very competitive' },
    'penn state': { avgGPA: 3.68, avgSAT: 1280, avgACT: 28, acceptanceRate: 76.0, selectivityLevel: 'moderately competitive' },
    'ohio state': { avgGPA: 3.76, avgSAT: 1320, avgACT: 29, acceptanceRate: 68.0, selectivityLevel: 'competitive' },
    'arizona state': { avgGPA: 3.54, avgSAT: 1230, avgACT: 26, acceptanceRate: 88.0, selectivityLevel: 'less competitive' },
    'florida': { avgGPA: 4.20, avgSAT: 1350, avgACT: 30, acceptanceRate: 31.0, selectivityLevel: 'very competitive' },
    'texas a&m': { avgGPA: 3.75, avgSAT: 1310, avgACT: 29, acceptanceRate: 63.0, selectivityLevel: 'competitive' }
  };

  // Find matching school
  for (const [key, data] of Object.entries(schoolDatabase)) {
    if (schoolLower.includes(key) || key.includes(schoolLower.split(' ')[0])) {
      return data;
    }
  }

  // Default for unknown schools
  return {
    avgGPA: 3.7,
    avgSAT: 1200,
    avgACT: 26,
    acceptanceRate: 60,
    selectivityLevel: 'competitive'
  };
}
