// Stripe configuration for payment processing
// This file contains the setup for Stripe integration

import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe with publishable key
const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || 'pk_test_demo_key'
);

export { stripePromise };

// Server-side Stripe configuration
export const stripeConfig = {
  secretKey: process.env.STRIPE_SECRET_KEY || 'sk_test_demo_key',
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || 'whsec_demo_secret',
  priceId: 'price_demo_premium', // Replace with actual Stripe price ID
};

// Payment intent creation function (for server-side use)
export async function createPaymentIntent(amount: number, metadata: any) {
  // This would be implemented with actual Stripe SDK in production
  // For demo purposes, we'll return a mock payment intent
  return {
    id: `pi_demo_${Date.now()}`,
    client_secret: `pi_demo_${Date.now()}_secret_demo`,
    amount,
    currency: 'usd',
    status: 'requires_payment_method',
    metadata,
  };
}

// Webhook handler for Stripe events
export function handleStripeWebhook(event: any) {
  switch (event.type) {
    case 'payment_intent.succeeded':
      // Handle successful payment
      console.log('Payment succeeded:', event.data.object);
      break;
    case 'payment_intent.payment_failed':
      // Handle failed payment
      console.log('Payment failed:', event.data.object);
      break;
    default:
      console.log(`Unhandled event type: ${event.type}`);
  }
}

// Product configuration
export const products = {
  premium: {
    name: 'Collegify Premium',
    description: 'Unlock detailed college insights and essay feedback',
    price: 1000, // $10.00 in cents
    features: [
      'Top 10 college matches',
      'Essay score (1-100)',
      'Detailed feedback',
      'Application tips',
    ],
  },
};
