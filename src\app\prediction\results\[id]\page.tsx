'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { MapPin, TrendingUp, Users, Star, Crown, Building, Shield, Search, CheckCircle, XCircle } from 'lucide-react';

interface ResultsPageProps {
  params: {
    id: string;
  };
}

export default function ResultsPage({ params }: ResultsPageProps) {
  const router = useRouter();
  const [prediction, setPrediction] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [schoolCheckInput, setSchoolCheckInput] = useState('');
  const [schoolCheckResult, setSchoolCheckResult] = useState<any>(null);
  const [isCheckingSchool, setIsCheckingSchool] = useState(false);

  useEffect(() => {
    fetchPrediction();
  }, [params.id]);

  const fetchPrediction = async () => {
    try {
      // Get real prediction data from sessionStorage
      const storedPrediction = sessionStorage.getItem(`prediction_${params.id}`);

      if (storedPrediction) {
        const predictionData = JSON.parse(storedPrediction);
        setPrediction(predictionData);
      } else {
        setError('No prediction data found. Please go back and submit the form again.');
      }
    } catch (error) {
      setError('Failed to load prediction data');
    } finally {
      setIsLoading(false);
    }
  };

  const checkSchoolAcceptance = async () => {
    if (!schoolCheckInput.trim() || !prediction) return;

    setIsCheckingSchool(true);
    try {
      const response = await fetch('/api/check-school', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          schoolName: schoolCheckInput,
          studentProfile: prediction.form_data
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setSchoolCheckResult(data.prediction);
      } else {
        setError('Failed to check school acceptance');
      }
    } catch (error) {
      setError('Failed to check school acceptance');
    } finally {
      setIsCheckingSchool(false);
    }
  };

  const getCollegeIcon = (type: string) => {
    switch (type) {
      case 'ivy':
        return <Crown className="w-6 h-6 text-yellow-500" />;
      case 'public':
        return <Building className="w-6 h-6 text-blue-500" />;
      case 'safety':
        return <Shield className="w-6 h-6 text-green-500" />;
      default:
        return <Star className="w-6 h-6 text-gray-500" />;
    }
  };

  const getCollegeTypeLabel = (type: string) => {
    switch (type) {
      case 'ivy':
        return 'Ivy League';
      case 'public':
        return 'Public State';
      case 'safety':
        return 'Safety School';
      default:
        return 'College';
    }
  };

  const getCollegeTypeDescription = (type: string) => {
    switch (type) {
      case 'ivy':
        return 'Highly competitive, prestigious institution';
      case 'public':
        return 'Excellent public university with strong academics';
      case 'safety':
        return 'Reliable option with good acceptance chances';
      default:
        return '';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your results...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <div className="text-red-500 mb-4">
            <Star className="w-16 h-16 mx-auto" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Error</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link 
            href="/prediction" 
            className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
          >
            Try Again
          </Link>
        </div>
      </div>
    );
  }

  if (!prediction) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Your College Predictions</h1>
          <p className="text-lg text-gray-600">
            AI-powered analysis with detailed essay feedback and college recommendations
          </p>
          <div className="mt-4 inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium">
            <Crown className="w-4 h-4 mr-2" />
            All Premium Features Included
          </div>
        </div>

        {/* College Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {prediction.suggested_colleges.map((college: College, index: number) => (
            <div key={college.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    {getCollegeIcon(college.type)}
                    <span className="ml-2 text-sm font-medium text-gray-600">
                      {getCollegeTypeLabel(college.type)}
                    </span>
                  </div>
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    #{index + 1}
                  </span>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-2">{college.name}</h3>
                
                <div className="flex items-center text-gray-600 mb-3">
                  <MapPin className="w-4 h-4 mr-1" />
                  <span className="text-sm">{college.location}</span>
                </div>

                <p className="text-gray-600 text-sm mb-4">{college.description}</p>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Acceptance Rate:</span>
                    <span className="font-medium">{college.acceptanceRate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Average GPA:</span>
                    <span className="font-medium">{college.averageGPA}</span>
                  </div>
                  {college.averageSAT && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Average SAT:</span>
                      <span className="font-medium">{college.averageSAT}</span>
                    </div>
                  )}
                  {college.averageACT && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Average ACT:</span>
                      <span className="font-medium">{college.averageACT}</span>
                    </div>
                  )}
                </div>

                <div className="mt-4 p-3 bg-gray-50 rounded">
                  <p className="text-xs text-gray-600">{getCollegeTypeDescription(college.type)}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Essay Analysis */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <div className="flex items-center mb-6">
            <Star className="w-6 h-6 text-yellow-500 mr-3" />
            <h2 className="text-2xl font-bold text-gray-900">Essay Analysis</h2>
            <span className="ml-3 bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">
              Included Free
            </span>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <div className="text-center mb-6">
                <div className="text-4xl font-bold text-blue-600 mb-2">{prediction.essay_analysis.score}/100</div>
                <p className="text-gray-600">Overall Essay Score</p>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">6 Key Factors Colleges Evaluate:</h3>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-700">Personal Growth</span>
                    <span className="font-medium text-blue-600">{prediction.essay_analysis.personalGrowthScore}/100</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${prediction.essay_analysis.personalGrowthScore}%` }}></div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-700">Authentic Voice</span>
                    <span className="font-medium text-blue-600">{prediction.essay_analysis.authenticVoiceScore}/100</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${prediction.essay_analysis.authenticVoiceScore}%` }}></div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-700">Specific Details</span>
                    <span className="font-medium text-blue-600">{prediction.essay_analysis.specificDetailsScore}/100</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${prediction.essay_analysis.specificDetailsScore}%` }}></div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-700">Impact Demonstration</span>
                    <span className="font-medium text-blue-600">{prediction.essay_analysis.impactDemonstrationScore}/100</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${prediction.essay_analysis.impactDemonstrationScore}%` }}></div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-700">Future Vision</span>
                    <span className="font-medium text-blue-600">{prediction.essay_analysis.futureVisionScore}/100</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${prediction.essay_analysis.futureVisionScore}%` }}></div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-700">Writing Quality</span>
                    <span className="font-medium text-blue-600">{prediction.essay_analysis.writingQualityScore}/100</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${prediction.essay_analysis.writingQualityScore}%` }}></div>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Detailed Feedback</h3>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Personal Growth</h4>
                  <p className="text-sm text-gray-600">{prediction.essay_analysis.detailedFeedback.personalGrowth}</p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Authentic Voice</h4>
                  <p className="text-sm text-gray-600">{prediction.essay_analysis.detailedFeedback.authenticVoice}</p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Specific Details</h4>
                  <p className="text-sm text-gray-600">{prediction.essay_analysis.detailedFeedback.specificDetails}</p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Impact Demonstration</h4>
                  <p className="text-sm text-gray-600">{prediction.essay_analysis.detailedFeedback.impactDemonstration}</p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Future Vision</h4>
                  <p className="text-sm text-gray-600">{prediction.essay_analysis.detailedFeedback.futureVision}</p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Writing Quality</h4>
                  <p className="text-sm text-gray-600">{prediction.essay_analysis.detailedFeedback.writingQuality}</p>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Overall Feedback</h4>
                <p className="text-sm text-blue-800">{prediction.essay_analysis.feedback}</p>
              </div>
            </div>
          </div>
        </div>

        {/* School Acceptance Checker */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <div className="flex items-center mb-6">
            <Search className="w-6 h-6 text-green-500 mr-3" />
            <h2 className="text-2xl font-bold text-gray-900">Check Any School</h2>
            <span className="ml-3 bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">
              AI-Powered
            </span>
          </div>

          <div className="max-w-2xl">
            <p className="text-gray-600 mb-6">
              Enter any college or university name to get an AI-powered acceptance prediction based on your profile.
            </p>

            <div className="flex gap-4 mb-6">
              <input
                type="text"
                value={schoolCheckInput}
                onChange={(e) => setSchoolCheckInput(e.target.value)}
                placeholder="Enter college name (e.g., Stanford University, MIT, etc.)"
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                onKeyPress={(e) => e.key === 'Enter' && checkSchoolAcceptance()}
              />
              <button
                onClick={checkSchoolAcceptance}
                disabled={!schoolCheckInput.trim() || isCheckingSchool}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isCheckingSchool ? 'Checking...' : 'Check School'}
              </button>
            </div>

            {schoolCheckResult && (
              <div className="border border-gray-200 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  {schoolCheckResult.decision === 'ACCEPT' ? (
                    <CheckCircle className="w-8 h-8 text-green-500 mr-3" />
                  ) : (
                    <XCircle className="w-8 h-8 text-red-500 mr-3" />
                  )}
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">{schoolCheckInput}</h3>
                    <p className={`text-lg font-semibold ${schoolCheckResult.decision === 'ACCEPT' ? 'text-green-600' : 'text-red-600'}`}>
                      {schoolCheckResult.decision} - {schoolCheckResult.confidence}% Confidence
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">AI Reasoning</h4>
                    <p className="text-gray-600">{schoolCheckResult.reasoning}</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Your Strengths</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {schoolCheckResult.strengths.map((strength: string, index: number) => (
                          <li key={index} className="flex items-center">
                            <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                            {strength}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Areas to Improve</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {schoolCheckResult.improvements.map((improvement: string, index: number) => (
                          <li key={index} className="flex items-center">
                            <TrendingUp className="w-4 h-4 text-blue-500 mr-2" />
                            {improvement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">School-Specific Tips</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {schoolCheckResult.schoolSpecificTips.map((tip: string, index: number) => (
                        <li key={index} className="flex items-center">
                          <Star className="w-4 h-4 text-yellow-500 mr-2" />
                          {tip}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Contact for Premium */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg shadow-md p-8 text-white text-center">
          <div className="max-w-3xl mx-auto">
            <Crown className="w-12 h-12 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-4">Want Even More Features?</h2>
            <p className="text-lg mb-6">
              Contact us to upgrade your account for additional premium features like personalized counseling sessions and application strategy.
            </p>
            <a
              href="mailto:<EMAIL>?subject=Premium Account Upgrade"
              className="bg-white text-purple-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors inline-block"
            >
              Email: <EMAIL>
            </a>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
          <Link 
            href="/prediction" 
            className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors text-center"
          >
            Make Another Prediction
          </Link>
          <Link 
            href="/dashboard" 
            className="border-2 border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors text-center"
          >
            View Dashboard
          </Link>
        </div>
      </div>
    </div>
  );
}
