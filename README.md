# Collegify - AI College Prediction App

A modern web application that helps students find their perfect college match using AI-powered predictions based on academic profiles, extracurriculars, and essays.

## Features

- **Home Page**: Welcome message and "Start Prediction" button
- **Authentication**: Google OAuth and email/password login
- **Prediction Form**: Comprehensive form with GPA, SAT/ACT scores, sports, extracurriculars, and essay input
- **College Recommendations**: AI suggests 3 colleges (Ivy League, public state, safety)
- **Premium Features**: $10 upgrade for top 10 matches, essay grading, and detailed feedback
- **User Dashboard**: Profile management, prediction history, and referral links
- **Mobile-Friendly**: Responsive design that works on all devices

## Tech Stack

- **Framework**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS
- **Authentication**: NextAuth.js with Google OAuth
- **Database**: Supabase (PostgreSQL)
- **Payments**: Stripe
- **Icons**: Lucide React
- **Testing**: Jest with React Testing Library

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account
- Google OAuth credentials
- Stripe account

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd collegify
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

Fill in the environment variables with your actual values.

4. Set up the database:
```bash
# Run the SQL schema in your Supabase SQL editor
cat database/schema.sql
```

5. Start the development server:
```bash
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## Testing

Run tests:
```bash
npm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

## Contact

For premium features and payment setup, contact: <EMAIL>

## Deployment

The easiest way to deploy is using [Vercel Platform](https://vercel.com/new). Set up your environment variables in the Vercel dashboard and deploy automatically on push.
