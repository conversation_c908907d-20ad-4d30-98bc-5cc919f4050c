'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { GraduationCap, BookOpen, Trophy, Users, Music, Palette, Briefcase, FlaskConical } from 'lucide-react';

export default function PredictionPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const [formData, setFormData] = useState({
    gpa: 0,
    testScore: 0,
    testType: 'SAT',
    sports: false,
    extracurriculars: {
      volunteering: false,
      leadership: false,
      clubs: false,
      music: false,
      art: false,
      internships: false,
      research: false,
      workExperience: false,
    },
    essay: '',
    // Additional detailed data for better AI predictions
    classRank: '',
    apCourses: '',
    honors: '',
    awards: '',
    communityService: '',
    workHours: '',
    familyIncome: '',
    firstGeneration: false,
    ethnicity: '',
    intendedMajor: '',
    careerGoals: '',
    challenges: '',
    uniqueExperiences: '',
  });

  // Redirect to sign in if not authenticated




  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/predictions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const result = await response.json();
        // Store prediction data in sessionStorage for the results page
        sessionStorage.setItem(`prediction_${result.predictionId}`, JSON.stringify(result.prediction));
        router.push(`/prediction/results/${result.predictionId}`);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'An error occurred while processing your prediction');
      }
    } catch (error) {
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExtracurricularChange = (key: keyof typeof formData.extracurriculars) => {
    setFormData(prev => ({
      ...prev,
      extracurriculars: {
        ...prev.extracurriculars,
        [key]: !prev.extracurriculars[key],
      },
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">College Prediction Form</h1>
          <p className="text-lg text-gray-600">
            Tell us about your academic profile and we'll suggest the perfect colleges for you!
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-8">
          {error && (
            <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Academic Information */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <BookOpen className="w-5 h-5 mr-2" />
                Academic Information
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="gpa" className="block text-sm font-medium text-gray-700 mb-2">
                    GPA (4.0 scale)
                  </label>
                  <input
                    type="number"
                    id="gpa"
                    min="0"
                    max="4.0"
                    step="0.01"
                    required
                    value={formData.gpa || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, gpa: parseFloat(e.target.value) || 0 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="3.75"
                  />
                </div>

                <div>
                  <label htmlFor="testType" className="block text-sm font-medium text-gray-700 mb-2">
                    Test Type
                  </label>
                  <select
                    id="testType"
                    value={formData.testType}
                    onChange={(e) => setFormData(prev => ({ ...prev, testType: e.target.value as 'SAT' | 'ACT' }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="SAT">SAT</option>
                    <option value="ACT">ACT</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="testScore" className="block text-sm font-medium text-gray-700 mb-2">
                    {formData.testType} Score
                  </label>
                  <input
                    type="number"
                    id="testScore"
                    min={formData.testType === 'SAT' ? 400 : 1}
                    max={formData.testType === 'SAT' ? 1600 : 36}
                    required
                    value={formData.testScore || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, testScore: parseInt(e.target.value) || 0 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder={formData.testType === 'SAT' ? '1450' : '32'}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {formData.testType === 'SAT' ? 'Range: 400-1600' : 'Range: 1-36'}
                  </p>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="sports"
                    checked={formData.sports}
                    onChange={(e) => setFormData(prev => ({ ...prev, sports: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="sports" className="ml-2 block text-sm text-gray-900 flex items-center">
                    <Trophy className="w-4 h-4 mr-1" />
                    Sports Participation
                  </label>
                </div>

                <div>
                  <label htmlFor="classRank" className="block text-sm font-medium text-gray-700 mb-2">
                    Class Rank (if available)
                  </label>
                  <input
                    type="text"
                    id="classRank"
                    value={formData.classRank}
                    onChange={(e) => setFormData(prev => ({ ...prev, classRank: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Top 10% or 15/300"
                  />
                </div>

                <div>
                  <label htmlFor="apCourses" className="block text-sm font-medium text-gray-700 mb-2">
                    AP/IB Courses Taken
                  </label>
                  <input
                    type="number"
                    id="apCourses"
                    value={formData.apCourses}
                    onChange={(e) => setFormData(prev => ({ ...prev, apCourses: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="5"
                    min="0"
                  />
                </div>

                <div>
                  <label htmlFor="intendedMajor" className="block text-sm font-medium text-gray-700 mb-2">
                    Intended Major
                  </label>
                  <input
                    type="text"
                    id="intendedMajor"
                    value={formData.intendedMajor}
                    onChange={(e) => setFormData(prev => ({ ...prev, intendedMajor: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Computer Science, Pre-Med, Business, etc."
                  />
                </div>

                <div>
                  <label htmlFor="workHours" className="block text-sm font-medium text-gray-700 mb-2">
                    Work Experience (hours/week)
                  </label>
                  <input
                    type="number"
                    id="workHours"
                    value={formData.workHours}
                    onChange={(e) => setFormData(prev => ({ ...prev, workHours: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="15"
                    min="0"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="firstGeneration"
                    checked={formData.firstGeneration}
                    onChange={(e) => setFormData(prev => ({ ...prev, firstGeneration: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="firstGeneration" className="ml-2 block text-sm text-gray-700">
                    First-generation college student
                  </label>
                </div>
              </div>
            </div>

            {/* Extracurricular Activities */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Extracurricular Activities
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {[
                  { key: 'volunteering', label: 'Volunteering', icon: Users },
                  { key: 'leadership', label: 'Leadership Roles', icon: Users },
                  { key: 'clubs', label: 'Clubs & Organizations', icon: Users },
                  { key: 'music', label: 'Music', icon: Music },
                  { key: 'art', label: 'Art & Creative', icon: Palette },
                  { key: 'internships', label: 'Internships', icon: Briefcase },
                  { key: 'research', label: 'Research', icon: FlaskConical },
                  { key: 'workExperience', label: 'Work Experience', icon: Briefcase },
                ].map(({ key, label, icon: Icon }) => (
                  <div key={key} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <input
                      type="checkbox"
                      id={key}
                      checked={formData.extracurriculars[key as keyof typeof formData.extracurriculars]}
                      onChange={() => handleExtracurricularChange(key as keyof typeof formData.extracurriculars)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor={key} className="ml-2 block text-sm text-gray-900 flex items-center">
                      <Icon className="w-4 h-4 mr-1" />
                      {label}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Additional Background Information */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Background & Goals
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="awards" className="block text-sm font-medium text-gray-700 mb-2">
                    Awards & Honors
                  </label>
                  <textarea
                    id="awards"
                    rows={3}
                    value={formData.awards}
                    onChange={(e) => setFormData(prev => ({ ...prev, awards: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="National Honor Society, Dean's List, Academic Awards, etc."
                  />
                </div>

                <div>
                  <label htmlFor="communityService" className="block text-sm font-medium text-gray-700 mb-2">
                    Community Service (hours)
                  </label>
                  <input
                    type="number"
                    id="communityService"
                    value={formData.communityService}
                    onChange={(e) => setFormData(prev => ({ ...prev, communityService: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="100"
                    min="0"
                  />
                </div>

                <div>
                  <label htmlFor="careerGoals" className="block text-sm font-medium text-gray-700 mb-2">
                    Career Goals
                  </label>
                  <textarea
                    id="careerGoals"
                    rows={3}
                    value={formData.careerGoals}
                    onChange={(e) => setFormData(prev => ({ ...prev, careerGoals: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="What do you want to do after college? Doctor, Engineer, Teacher, etc."
                  />
                </div>

                <div>
                  <label htmlFor="challenges" className="block text-sm font-medium text-gray-700 mb-2">
                    Challenges Overcome
                  </label>
                  <textarea
                    id="challenges"
                    rows={3}
                    value={formData.challenges}
                    onChange={(e) => setFormData(prev => ({ ...prev, challenges: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Personal, academic, or financial challenges you've overcome"
                  />
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="uniqueExperiences" className="block text-sm font-medium text-gray-700 mb-2">
                    Unique Experiences or Skills
                  </label>
                  <textarea
                    id="uniqueExperiences"
                    rows={3}
                    value={formData.uniqueExperiences}
                    onChange={(e) => setFormData(prev => ({ ...prev, uniqueExperiences: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Languages spoken, travel experiences, unique hobbies, special talents, etc."
                  />
                </div>
              </div>
            </div>

            {/* Essay */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <GraduationCap className="w-5 h-5 mr-2" />
                College Essay
              </h2>
              
              <div>
                <label htmlFor="essay" className="block text-sm font-medium text-gray-700 mb-2">
                  Paste your college essay here
                </label>
                <textarea
                  id="essay"
                  rows={8}
                  required
                  value={formData.essay}
                  onChange={(e) => setFormData(prev => ({ ...prev, essay: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Paste your college essay here. This will help us provide better recommendations and, if you upgrade to premium, give you detailed feedback on your essay."
                />
                <p className="text-xs text-gray-500 mt-1">
                  Your essay helps us understand your personality and goals for better college matching.
                </p>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-center">
              <button
                type="submit"
                disabled={isLoading}
                className="bg-blue-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Analyzing...' : 'Get My College Predictions'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
