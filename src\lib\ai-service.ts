import { GoogleGenerativeAI } from '@google/generative-ai';

// College data
export interface College {
  id: string;
  name: string;
  type: 'ivy' | 'public' | 'safety';
  location: string;
  acceptanceRate: number;
  averageGPA: number;
  averageSAT: number;
  averageACT: number;
}

export const ivyLeagueColleges: College[] = [
  { id: '1', name: 'Harvard University', type: 'ivy', location: 'Cambridge, MA', acceptanceRate: 3.4, averageGPA: 4.18, averageSAT: 1520, averageACT: 34 },
  { id: '2', name: 'Yale University', type: 'ivy', location: 'New Haven, CT', acceptanceRate: 4.6, averageGPA: 4.14, averageSAT: 1515, averageACT: 34 },
  { id: '3', name: 'Princeton University', type: 'ivy', location: 'Princeton, NJ', acceptanceRate: 4.0, averageGPA: 4.16, averageSAT: 1510, averageACT: 34 },
  { id: '4', name: 'Columbia University', type: 'ivy', location: 'New York, NY', acceptanceRate: 3.9, averageGPA: 4.15, averageSAT: 1505, averageACT: 34 },
  { id: '5', name: 'University of Pennsylvania', type: 'ivy', location: 'Philadelphia, PA', acceptanceRate: 5.9, averageGPA: 4.10, averageSAT: 1500, averageACT: 33 },
  { id: '6', name: 'Dartmouth College', type: 'ivy', location: 'Hanover, NH', acceptanceRate: 6.2, averageGPA: 4.11, averageSAT: 1490, averageACT: 33 },
  { id: '7', name: 'Brown University', type: 'ivy', location: 'Providence, RI', acceptanceRate: 5.4, averageGPA: 4.08, averageSAT: 1485, averageACT: 33 },
  { id: '8', name: 'Cornell University', type: 'ivy', location: 'Ithaca, NY', acceptanceRate: 8.7, averageGPA: 4.05, averageSAT: 1470, averageACT: 33 }
];

export const publicStateColleges: College[] = [
  { id: '9', name: 'UC Berkeley', type: 'public', location: 'Berkeley, CA', acceptanceRate: 14.5, averageGPA: 4.25, averageSAT: 1430, averageACT: 32 },
  { id: '10', name: 'UCLA', type: 'public', location: 'Los Angeles, CA', acceptanceRate: 12.3, averageGPA: 4.23, averageSAT: 1425, averageACT: 32 },
  { id: '11', name: 'University of Michigan', type: 'public', location: 'Ann Arbor, MI', acceptanceRate: 20.2, averageGPA: 4.00, averageSAT: 1420, averageACT: 32 },
  { id: '12', name: 'University of Virginia', type: 'public', location: 'Charlottesville, VA', acceptanceRate: 21.0, averageGPA: 4.05, averageSAT: 1410, averageACT: 31 },
  { id: '13', name: 'Georgia Tech', type: 'public', location: 'Atlanta, GA', acceptanceRate: 17.0, averageGPA: 4.10, averageSAT: 1400, averageACT: 31 },
  { id: '14', name: 'University of North Carolina', type: 'public', location: 'Chapel Hill, NC', acceptanceRate: 19.2, averageGPA: 4.15, averageSAT: 1390, averageACT: 31 },
  { id: '15', name: 'University of Texas Austin', type: 'public', location: 'Austin, TX', acceptanceRate: 31.8, averageGPA: 3.95, averageSAT: 1370, averageACT: 30 }
];

export const safetyColleges: College[] = [
  { id: '16', name: 'Arizona State University', type: 'safety', location: 'Tempe, AZ', acceptanceRate: 88.0, averageGPA: 3.54, averageSAT: 1230, averageACT: 26 },
  { id: '17', name: 'Penn State', type: 'safety', location: 'University Park, PA', acceptanceRate: 76.0, averageGPA: 3.68, averageSAT: 1280, averageACT: 28 },
  { id: '18', name: 'Ohio State University', type: 'safety', location: 'Columbus, OH', acceptanceRate: 68.0, averageGPA: 3.76, averageSAT: 1320, averageACT: 29 },
  { id: '19', name: 'University of Florida', type: 'safety', location: 'Gainesville, FL', acceptanceRate: 31.0, averageGPA: 4.20, averageSAT: 1350, averageACT: 30 },
  { id: '20', name: 'Texas A&M', type: 'safety', location: 'College Station, TX', acceptanceRate: 63.0, averageGPA: 3.75, averageSAT: 1310, averageACT: 29 },
  { id: '21', name: 'Indiana University', type: 'safety', location: 'Bloomington, IN', acceptanceRate: 82.0, averageGPA: 3.65, averageSAT: 1260, averageACT: 27 },
  { id: '22', name: 'University of Alabama', type: 'safety', location: 'Tuscaloosa, AL', acceptanceRate: 83.0, averageGPA: 3.71, averageSAT: 1240, averageACT: 27 }
];

// Initialize the Gemini AI client
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY || 'demo-key');

export interface StudentProfile {
  gpa: number;
  testScore: number;
  testType: 'SAT' | 'ACT';
  hasSports: boolean;
  extracurriculars: {
    volunteering: boolean;
    leadership: boolean;
    clubs: boolean;
    music: boolean;
    art: boolean;
    internships: boolean;
    research: boolean;
    workExperience: boolean;
  };
  essay: string;
}

export interface AICollegeRecommendation {
  college: College;
  matchPercentage: number;
  reasoning: string;
  strengthsAlignment: string[];
  improvementAreas: string[];
}

export interface AIEssayAnalysis {
  score: number;
  feedback: string;
  strengths: string[];
  improvements: string[];
  wordCount: number;
  readabilityScore: number;
  // 6 Key Factors Colleges Want
  personalGrowthScore: number; // Shows maturity and self-reflection
  authenticVoiceScore: number; // Genuine personality and unique perspective
  specificDetailsScore: number; // Concrete examples and vivid storytelling
  impactDemonstrationScore: number; // Shows how you've made a difference
  futureVisionScore: number; // Clear goals and how college fits
  writingQualityScore: number; // Grammar, structure, and clarity
  detailedFeedback: {
    personalGrowth: string;
    authenticVoice: string;
    specificDetails: string;
    impactDemonstration: string;
    futureVision: string;
    writingQuality: string;
  };
}

export async function getAICollegeRecommendations(
  profile: StudentProfile,
  isPremium: boolean = false
): Promise<AICollegeRecommendation[]> {
  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Safely extract extracurriculars
    const extracurriculars = profile.extracurriculars ?
      Object.entries(profile.extracurriculars)
        .filter(([_, value]) => value)
        .map(([key, _]) => key)
        .join(', ') : 'None specified';

    const prompt = `You are an expert college admissions counselor with 20+ years of experience. Analyze this student profile and provide REALISTIC college recommendations with ACCURATE match percentages.

STUDENT PROFILE:
- GPA: ${profile.gpa}/4.0 (Unweighted)
- ${profile.testType} Score: ${profile.testScore}
- Sports Participation: ${profile.hasSports ? 'Yes' : 'No'}
- Extracurriculars: ${extracurriculars}
- Essay Quality: ${profile.essay ? `${profile.essay.length} characters provided` : 'Not provided'}

CRITICAL ANALYSIS FACTORS:
1. Academic Stats vs College Averages (40% weight)
2. Extracurricular Strength & Leadership (25% weight)
3. Sports/Special Talents (15% weight)
4. Essay Quality & Personal Story (20% weight)

AVAILABLE COLLEGES WITH REAL ADMISSION DATA:
${isPremium ?
  [...ivyLeagueColleges, ...publicStateColleges, ...safetyColleges].map(c =>
    `- ${c.name} (${c.type.toUpperCase()}) | Accept Rate: ${c.acceptanceRate}% | Avg GPA: ${c.averageGPA} | Avg ${profile.testType}: ${profile.testType === 'SAT' ? c.averageSAT : c.averageACT} | Difficulty: ${c.acceptanceRate < 10 ? 'EXTREMELY HARD' : c.acceptanceRate < 25 ? 'VERY HARD' : c.acceptanceRate < 50 ? 'MODERATE' : 'ACCESSIBLE'}`
  ).join('\n') :
  [ivyLeagueColleges[0], publicStateColleges[0], safetyColleges[0]].map(c =>
    `- ${c.name} (${c.type.toUpperCase()}) | Accept Rate: ${c.acceptanceRate}% | Avg GPA: ${c.averageGPA} | Avg ${profile.testType}: ${profile.testType === 'SAT' ? c.averageSAT : c.averageACT} | Difficulty: ${c.acceptanceRate < 10 ? 'EXTREMELY HARD' : c.acceptanceRate < 25 ? 'VERY HARD' : c.acceptanceRate < 50 ? 'MODERATE' : 'ACCESSIBLE'}`
  ).join('\n')
}

MATCH PERCENTAGE GUIDELINES:
- 90-100%: Student significantly exceeds averages + strong ECs + compelling story
- 80-89%: Student meets/exceeds averages + good ECs
- 70-79%: Student slightly below averages but strong in other areas
- 60-69%: Student below averages but has potential
- 50-59%: Reach school, significant gaps in profile
- Below 50%: Not recommended

TASK: Select ${isPremium ? '10' : '3'} colleges that provide a realistic range (reach, match, safety) and calculate ACCURATE match percentages based on real admission standards.

RESPOND WITH VALID JSON ONLY:
[
  {
    "collegeName": "Harvard University",
    "matchPercentage": 65,
    "reasoning": "Your 3.9 GPA is slightly below Harvard's 4.0 average, but your 1520 SAT matches their median. Strong extracurriculars help, but Harvard admits only 3.4% of applicants, making this a reach school.",
    "strengthsAlignment": ["Competitive test scores", "Strong academic foundation"],
    "improvementAreas": ["Need exceptional achievements to stand out", "Strengthen leadership profile"]
  }
]`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();



    // Clean and parse the AI response
    const cleanText = text.replace(/```json\n?|\n?```/g, '').trim();
    const aiRecommendations = JSON.parse(cleanText);

    // Map to our college database with better matching
    const allColleges = [...ivyLeagueColleges, ...publicStateColleges, ...safetyColleges];
    const recommendations: AICollegeRecommendation[] = aiRecommendations.map((rec: any) => {
      // Try exact name match first
      let college = allColleges.find(c =>
        c.name.toLowerCase() === rec.collegeName.toLowerCase()
      );

      // If no exact match, try partial match
      if (!college) {
        college = allColleges.find(c =>
          c.name.toLowerCase().includes(rec.collegeName.toLowerCase()) ||
          rec.collegeName.toLowerCase().includes(c.name.toLowerCase().split(' ')[0])
        );
      }

      // If still no match, use a fallback based on match percentage
      if (!college) {
        if (rec.matchPercentage > 80) {
          college = safetyColleges[0];
        } else if (rec.matchPercentage > 60) {
          college = publicStateColleges[0];
        } else {
          college = ivyLeagueColleges[0];
        }
      }

      return {
        college,
        matchPercentage: Math.min(95, Math.max(25, rec.matchPercentage || 75)),
        reasoning: rec.reasoning || `Based on your ${profile.gpa} GPA and ${profile.testScore} ${profile.testType} score, this represents a ${rec.matchPercentage > 70 ? 'good' : 'challenging'} match.`,
        strengthsAlignment: rec.strengthsAlignment || ['Academic foundation'],
        improvementAreas: rec.improvementAreas || ['Continue building strong profile']
      };
    });

    return recommendations;
  } catch (error) {
    console.error('AI recommendation error:', error);

    // Fallback to rule-based recommendations
    return getFallbackRecommendations(profile, isPremium);
  }
}

export async function getAIEssayAnalysis(essay: string): Promise<AIEssayAnalysis> {
  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    const prompt = `You are an expert college admissions essay reviewer. Analyze this essay and score it on 6 key factors:

ESSAY TO ANALYZE:
"${essay}"

SCORE each factor from 0-100:
1. PERSONAL GROWTH: Maturity, self-reflection, learning from experiences
2. AUTHENTIC VOICE: Genuine personality and unique perspective
3. SPECIFIC DETAILS: Concrete examples and vivid storytelling
4. IMPACT DEMONSTRATION: Shows difference made or challenges overcome
5. FUTURE VISION: Clear goals and how college fits plans
6. WRITING QUALITY: Grammar, structure, clarity

RESPOND WITH VALID JSON ONLY:
{
  "score": 85,
  "feedback": "Your essay demonstrates strong writing with clear examples...",
  "strengths": ["Strong personal voice", "Good specific details"],
  "improvements": ["Add more future vision", "Strengthen impact section"],
  "readabilityScore": 88,
  "personalGrowthScore": 85,
  "authenticVoiceScore": 90,
  "specificDetailsScore": 82,
  "impactDemonstrationScore": 78,
  "futureVisionScore": 75,
  "writingQualityScore": 92,
  "detailedFeedback": {
    "personalGrowth": "Shows good self-reflection and maturity...",
    "authenticVoice": "Your unique personality comes through clearly...",
    "specificDetails": "Good use of concrete examples...",
    "impactDemonstration": "Could highlight more specific contributions...",
    "futureVision": "Articulate clearer goals and college connection...",
    "writingQuality": "Strong grammar and structure..."
  }
}`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();



    const cleanText = text.replace(/```json\n?|\n?```/g, '').trim();
    const analysis = JSON.parse(cleanText);

    // Ensure all required fields exist with defaults
    return {
      score: analysis.score || 75,
      feedback: analysis.feedback || 'Your essay shows good potential with room for improvement.',
      strengths: analysis.strengths || ['Clear writing'],
      improvements: analysis.improvements || ['Add more specific details'],
      wordCount: essay.split(/\s+/).length,
      readabilityScore: analysis.readabilityScore || 80,
      personalGrowthScore: analysis.personalGrowthScore || 75,
      authenticVoiceScore: analysis.authenticVoiceScore || 75,
      specificDetailsScore: analysis.specificDetailsScore || 75,
      impactDemonstrationScore: analysis.impactDemonstrationScore || 75,
      futureVisionScore: analysis.futureVisionScore || 75,
      writingQualityScore: analysis.writingQualityScore || 80,
      detailedFeedback: {
        personalGrowth: analysis.detailedFeedback?.personalGrowth || 'Shows potential for growth and self-reflection.',
        authenticVoice: analysis.detailedFeedback?.authenticVoice || 'Your personality comes through in the writing.',
        specificDetails: analysis.detailedFeedback?.specificDetails || 'Consider adding more concrete examples.',
        impactDemonstration: analysis.detailedFeedback?.impactDemonstration || 'Highlight your specific contributions and impact.',
        futureVision: analysis.detailedFeedback?.futureVision || 'Articulate your goals and how college fits your plans.',
        writingQuality: analysis.detailedFeedback?.writingQuality || 'Good writing mechanics and clarity.'
      }
    };
  } catch (error) {
    console.error('AI essay analysis error:', error);

    // Fallback to rule-based analysis
    return getFallbackEssayAnalysis(essay);
  }
}

function getFallbackRecommendations(profile: StudentProfile, isPremium: boolean): AICollegeRecommendation[] {
  // Select a realistic mix based on student profile
  let selectedColleges: College[] = [];

  if (isPremium) {
    // For premium, provide a realistic range of 10 colleges
    if (profile.gpa >= 3.8 && profile.testScore >= 1450) {
      // High achiever - include reach schools
      selectedColleges = [
        ...ivyLeagueColleges.slice(0, 3),
        ...publicStateColleges.slice(0, 4),
        ...safetyColleges.slice(0, 3)
      ];
    } else if (profile.gpa >= 3.5 && profile.testScore >= 1300) {
      // Good student - focus on match and safety schools
      selectedColleges = [
        ...ivyLeagueColleges.slice(-1), // Just one reach
        ...publicStateColleges.slice(0, 5),
        ...safetyColleges.slice(0, 4)
      ];
    } else {
      // Focus on safety and accessible schools
      selectedColleges = [
        ...publicStateColleges.slice(-2), // Just 2 public schools
        ...safetyColleges
      ];
    }
  } else {
    // For free version, provide 3 realistic colleges
    if (profile.gpa >= 3.8 && profile.testScore >= 1450) {
      selectedColleges = [ivyLeagueColleges[0], publicStateColleges[0], safetyColleges[0]];
    } else if (profile.gpa >= 3.5 && profile.testScore >= 1300) {
      selectedColleges = [publicStateColleges[0], publicStateColleges[1], safetyColleges[0]];
    } else {
      selectedColleges = [publicStateColleges[publicStateColleges.length - 1], safetyColleges[0], safetyColleges[1]];
    }
  }

  return selectedColleges.map(college => {
    // More sophisticated matching algorithm
    const gpaRatio = profile.gpa / college.averageGPA;
    const testTarget = profile.testType === 'SAT' ? college.averageSAT : college.averageACT;
    const testRatio = profile.testScore / testTarget;

    // Base academic score (0-100)
    let academicScore = ((gpaRatio + testRatio) / 2) * 100;

    // Adjust for college selectivity (more selective = harder to get in)
    const selectivityPenalty = college.acceptanceRate < 10 ? 25 :
                              college.acceptanceRate < 25 ? 15 :
                              college.acceptanceRate < 50 ? 5 : 0;

    academicScore -= selectivityPenalty;

    // Extracurricular bonus
    const extracurricularCount = profile.extracurriculars ? Object.values(profile.extracurriculars).filter(Boolean).length : 0;
    const ecBonus = Math.min(12, extracurricularCount * 2);

    // Sports bonus for competitive schools
    const sportsBonus = profile.hasSports && college.acceptanceRate < 25 ? 3 : 0;

    // Essay bonus
    const essayBonus = profile.essay && profile.essay.length > 200 ? 3 : 0;

    const finalScore = Math.round(academicScore + ecBonus + sportsBonus + essayBonus);
    const matchPercentage = Math.min(90, Math.max(15, finalScore));

    // Realistic reasoning based on stats
    const gpaComparison = profile.gpa >= college.averageGPA ? 'meets/exceeds' : 'falls below';
    const testComparison = profile.testScore >= testTarget ? 'competitive' : 'below average';

    return {
      college,
      matchPercentage,
      reasoning: `Your ${profile.gpa} GPA ${gpaComparison} ${college.name}'s ${college.averageGPA} average, and your ${profile.testScore} ${profile.testType} is ${testComparison} for their ${testTarget} average. With ${college.acceptanceRate}% acceptance rate, this is a ${matchPercentage > 70 ? 'good match' : matchPercentage > 50 ? 'possible match' : 'reach'} school.`,
      strengthsAlignment: [
        ...(profile.gpa >= college.averageGPA ? ['Strong GPA'] : []),
        ...(profile.testScore >= testTarget ? ['Competitive test scores'] : []),
        ...(extracurricularCount > 2 ? ['Good extracurricular involvement'] : []),
        ...(profile.hasSports ? ['Athletic participation'] : [])
      ].slice(0, 3),
      improvementAreas: [
        ...(profile.gpa < college.averageGPA ? ['Improve GPA if possible'] : []),
        ...(profile.testScore < testTarget ? ['Consider retaking standardized tests'] : []),
        ...(extracurricularCount < 3 ? ['Strengthen extracurricular profile'] : []),
        'Write compelling essays that showcase your unique story'
      ].slice(0, 2)
    };
  });
}

function getFallbackEssayAnalysis(essay: string): AIEssayAnalysis {
  const wordCount = essay.split(/\s+/).length;
  const sentenceCount = essay.split(/[.!?]+/).length - 1;
  const avgWordsPerSentence = wordCount / Math.max(1, sentenceCount);

  // Basic scoring algorithm for 6 key factors
  const lengthScore = Math.min(100, (wordCount / 500) * 100);
  const readabilityScore = avgWordsPerSentence > 25 ? 60 : avgWordsPerSentence < 10 ? 70 : 85;

  // 6 Key Factors (simplified scoring)
  const personalGrowthScore = essay.toLowerCase().includes('learn') || essay.toLowerCase().includes('grow') ? 75 : 65;
  const authenticVoiceScore = wordCount > 200 ? 80 : 65;
  const specificDetailsScore = essay.includes('when') || essay.includes('where') ? 85 : 70;
  const impactDemonstrationScore = essay.toLowerCase().includes('help') || essay.toLowerCase().includes('impact') ? 80 : 65;
  const futureVisionScore = essay.toLowerCase().includes('goal') || essay.toLowerCase().includes('future') ? 85 : 70;
  const writingQualityScore = readabilityScore;

  const overallScore = Math.round((personalGrowthScore + authenticVoiceScore + specificDetailsScore + impactDemonstrationScore + futureVisionScore + writingQualityScore) / 6);

  return {
    score: overallScore,
    feedback: `Your essay shows ${overallScore > 80 ? 'strong' : overallScore > 60 ? 'good' : 'developing'} writing skills across the key factors colleges evaluate. ${wordCount < 300 ? 'Consider expanding with more specific examples and details.' : 'Good length and detail.'}`,
    strengths: wordCount > 300 ? ['Good length and detail', 'Clear expression'] : ['Clear expression'],
    improvements: wordCount < 300 ? ['Expand with more specific examples', 'Add more personal reflection'] : ['Enhance with more vivid details'],
    wordCount,
    readabilityScore,
    personalGrowthScore,
    authenticVoiceScore,
    specificDetailsScore,
    impactDemonstrationScore,
    futureVisionScore,
    writingQualityScore,
    detailedFeedback: {
      personalGrowth: personalGrowthScore > 70 ? 'Shows good self-reflection and growth mindset.' : 'Consider adding more examples of personal growth and learning.',
      authenticVoice: authenticVoiceScore > 70 ? 'Writing feels genuine and personal.' : 'Try to let your unique personality shine through more.',
      specificDetails: specificDetailsScore > 70 ? 'Good use of specific examples and details.' : 'Add more concrete examples and vivid storytelling.',
      impactDemonstration: impactDemonstrationScore > 70 ? 'Shows positive impact and contribution.' : 'Highlight how you\'ve made a difference or overcome challenges.',
      futureVision: futureVisionScore > 70 ? 'Clear sense of goals and direction.' : 'Articulate your future goals and how college fits your plans.',
      writingQuality: writingQualityScore > 70 ? 'Strong writing mechanics and clarity.' : 'Focus on improving grammar, structure, and clarity.'
    }
  };
}
