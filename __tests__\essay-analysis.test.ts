import { analyzeEssay } from '../src/lib/essay-analysis';

describe('analyzeEssay', () => {
  test('should analyze a well-written essay with high score', () => {
    const essay = `
      When I was twelve years old, I discovered my passion for helping others through volunteering at the local animal shelter. 
      This experience taught me the importance of compassion and dedication. Every weekend, I would spend hours caring for 
      abandoned animals, learning about responsibility and empathy. Through this volunteer work, I realized that I wanted to 
      pursue a career in veterinary medicine. The challenges I faced while working with sick animals taught me perseverance 
      and problem-solving skills. I learned that even small acts of kindness can make a significant difference in the world. 
      This experience shaped my character and influenced my future goals. I plan to continue this work throughout college 
      and beyond, using my education to help both animals and their human companions. My time at the shelter showed me that 
      true fulfillment comes from serving others and making a positive impact on the community.
    `;

    const analysis = analyzeEssay(essay);

    expect(analysis.score).toBeGreaterThan(70);
    expect(analysis.score).toBeLessThanOrEqual(100);
    expect(analysis.feedback).toContain('essay');
    expect(analysis.strengths).toHaveLength.toBeGreaterThan(0);
    expect(analysis.improvements).toHaveLength.toBeGreaterThan(0);
    expect(analysis.wordCount).toBeGreaterThan(100);
  });

  test('should analyze a short essay with lower score', () => {
    const essay = 'I like college. College is good. I want to go to college.';

    const analysis = analyzeEssay(essay);

    expect(analysis.score).toBeLessThan(70);
    expect(analysis.wordCount).toBeLessThan(50);
    expect(analysis.improvements).toContain('Expand with more specific examples and details');
  });

  test('should identify personal growth indicators', () => {
    const essay = `
      Through my experience as a team captain, I learned valuable leadership skills. I discovered that effective 
      communication is essential for team success. This challenge taught me to persevere through difficult situations. 
      I realized that my role was not just to lead, but to support and encourage my teammates. This experience helped 
      me grow as a person and develop confidence in my abilities.
    `;

    const analysis = analyzeEssay(essay);

    expect(analysis.strengths).toContain('Shows personal growth and self-reflection');
  });

  test('should handle empty or very short essays', () => {
    const essay = '';

    const analysis = analyzeEssay(essay);

    expect(analysis.score).toBeLessThan(50);
    expect(analysis.wordCount).toBe(0);
  });

  test('should calculate readability score', () => {
    const essay = `
      This is a test essay with moderate complexity. The sentences are of varying lengths to test readability. 
      Some sentences are short. Others are longer and contain more complex ideas that require careful consideration 
      and analysis. The vocabulary includes both simple and more sophisticated words to create diversity.
    `;

    const analysis = analyzeEssay(essay);

    expect(analysis.readabilityScore).toBeGreaterThan(0);
    expect(analysis.readabilityScore).toBeLessThanOrEqual(100);
  });
});
