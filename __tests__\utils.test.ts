import { calculateCollegeMatch } from '../src/lib/utils';

describe('calculateCollegeMatch', () => {
  test('should return high score for excellent academic profile', () => {
    const score = calculateCollegeMatch(
      4.0, // GPA
      1550, // SAT score
      'SAT',
      {
        volunteering: true,
        leadership: true,
        clubs: true,
        music: true,
        art: false,
        internships: true,
        research: true,
        workExperience: false,
      }
    );

    expect(score).toBeGreaterThan(90);
    expect(score).toBeLessThanOrEqual(100);
  });

  test('should return moderate score for average academic profile', () => {
    const score = calculateCollegeMatch(
      3.5, // GPA
      1300, // SAT score
      'SAT',
      {
        volunteering: true,
        leadership: false,
        clubs: true,
        music: false,
        art: false,
        internships: false,
        research: false,
        workExperience: true,
      }
    );

    expect(score).toBeGreaterThan(50);
    expect(score).toBeLessThan(80);
  });

  test('should return lower score for below-average academic profile', () => {
    const score = calculateCollegeMatch(
      2.8, // GPA
      1100, // SAT score
      'SAT',
      {
        volunteering: false,
        leadership: false,
        clubs: false,
        music: false,
        art: false,
        internships: false,
        research: false,
        workExperience: false,
      }
    );

    expect(score).toBeLessThan(60);
    expect(score).toBeGreaterThan(0);
  });

  test('should handle ACT scores correctly', () => {
    const score = calculateCollegeMatch(
      3.8, // GPA
      34, // ACT score
      'ACT',
      {
        volunteering: true,
        leadership: true,
        clubs: true,
        music: false,
        art: false,
        internships: false,
        research: false,
        workExperience: false,
      }
    );

    expect(score).toBeGreaterThan(80);
    expect(score).toBeLessThanOrEqual(100);
  });

  test('should never exceed 100 or go below 0', () => {
    // Test with extremely high values
    const highScore = calculateCollegeMatch(
      4.5, // Impossible GPA
      1700, // Impossible SAT
      'SAT',
      {
        volunteering: true,
        leadership: true,
        clubs: true,
        music: true,
        art: true,
        internships: true,
        research: true,
        workExperience: true,
      }
    );

    expect(highScore).toBeLessThanOrEqual(100);

    // Test with extremely low values
    const lowScore = calculateCollegeMatch(
      0, // Very low GPA
      400, // Minimum SAT
      'SAT',
      {
        volunteering: false,
        leadership: false,
        clubs: false,
        music: false,
        art: false,
        internships: false,
        research: false,
        workExperience: false,
      }
    );

    expect(lowScore).toBeGreaterThanOrEqual(0);
  });
});
