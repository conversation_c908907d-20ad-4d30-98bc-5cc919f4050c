@import "tailwindcss";

/* Force light mode for consistent design */
:root {
  --background: #ffffff;
  --foreground: #171717;
  color-scheme: light;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Override dark mode preference to maintain light theme */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff;
    --foreground: #171717;
    color-scheme: light;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Ensure good contrast for all text elements */
.text-white {
  color: #ffffff !important;
}

.text-gray-600 {
  color: #4b5563 !important;
}

.text-gray-700 {
  color: #374151 !important;
}

.text-gray-900 {
  color: #111827 !important;
}

/* Improve visibility of loading spinners */
.animate-spin {
  border-color: transparent;
}

.border-blue-600 {
  border-color: #2563eb !important;
}

/* Ensure form inputs have good contrast */
input, textarea, select {
  background-color: #ffffff !important;
  color: #171717 !important;
  border-color: #d1d5db !important;
}

input:focus, textarea:focus, select:focus {
  border-color: #2563eb !important;
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

/* Ensure placeholder text is visible */
input::placeholder, textarea::placeholder {
  color: #6b7280 !important;
  opacity: 1 !important;
}

/* Improve button contrast */
.bg-blue-600 {
  background-color: #2563eb !important;
}

.bg-blue-600:hover {
  background-color: #1d4ed8 !important;
}

/* Ensure links are visible */
a {
  color: inherit;
}

a.text-blue-600 {
  color: #2563eb !important;
}

a.text-blue-600:hover {
  color: #1d4ed8 !important;
}

/* Improve small text visibility */
.text-xs {
  color: #4b5563 !important;
}

.text-sm {
  color: #374151 !important;
}
